/**
 * eSSL Fingerprint Enrollment System - Frontend JavaScript
 * Handles form interactions, API calls, and UI updates
 */

class FingerprintEnrollmentApp {
    constructor() {
        this.enrollmentKey = null;
        this.enrollmentInProgress = false;
        this.statusCheckInterval = null;

        this.initializeElements();
        this.bindEvents();

        // Don't validate form immediately - let user enter data first
        // Enable test connection button by default
        if (this.testConnectionBtn) {
            this.testConnectionBtn.disabled = false;
        }
    }
    
    initializeElements() {
        // Form elements
        this.enrollmentForm = document.getElementById('enrollmentForm');
        this.staffIdInput = document.getElementById('staffId');
        this.staffNameInput = document.getElementById('staffName');
        this.deviceIpInput = document.getElementById('deviceIp');
        
        // Buttons
        this.testConnectionBtn = document.getElementById('testConnectionBtn');
        this.enrollBtn = document.getElementById('enrollBtn');
        this.registerFingerprintBtn = document.getElementById('registerFingerprintBtn');
        this.cancelEnrollmentBtn = document.getElementById('cancelEnrollmentBtn');
        
        // Status and display elements
        this.statusMessages = document.getElementById('statusMessages');
        this.detailsSection = document.getElementById('detailsSection');
        this.userInfo = document.getElementById('userInfo');
        this.progressIndicator = document.getElementById('progressIndicator');
        this.fingerprintInstructions = document.getElementById('fingerprintInstructions');
        
        // Display elements
        this.displayStaffId = document.getElementById('displayStaffId');
        this.displayStaffName = document.getElementById('displayStaffName');
        this.displayDeviceIp = document.getElementById('displayDeviceIp');
        this.enrollmentStatus = document.getElementById('enrollmentStatus');
        
        // Error message elements
        this.staffIdError = document.getElementById('staffIdError');
        this.deviceIpError = document.getElementById('deviceIpError');
        
        // Loading elements
        this.testLoading = document.getElementById('testLoading');
        this.enrollLoading = document.getElementById('enrollLoading');
        this.fingerprintLoading = document.getElementById('fingerprintLoading');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
    }
    
    bindEvents() {
        // Form submission
        this.enrollmentForm.addEventListener('submit', (e) => this.handleEnrollment(e));
        
        // Button clicks
        this.testConnectionBtn.addEventListener('click', () => this.testConnection());
        this.registerFingerprintBtn.addEventListener('click', () => this.registerFingerprint());
        this.cancelEnrollmentBtn.addEventListener('click', () => this.cancelEnrollment());
        
        // Input validation
        this.staffIdInput.addEventListener('input', () => {
            this.validateStaffId();
            this.validateForm();
        });
        this.deviceIpInput.addEventListener('input', () => {
            this.validateDeviceIp();
            this.validateForm();
        });
        this.staffNameInput.addEventListener('input', () => this.validateStaffName());

        // Real-time form validation
        this.staffIdInput.addEventListener('blur', () => {
            this.validateStaffId();
            this.validateForm();
        });
        this.deviceIpInput.addEventListener('blur', () => {
            this.validateDeviceIp();
            this.validateForm();
        });
    }
    
    validateForm() {
        const isValid = this.validateStaffId() && this.validateDeviceIp();
        this.enrollBtn.disabled = !isValid;

        // Only disable test connection button if user has entered something invalid
        // Don't disable it if the field is empty (let user enter IP first)
        const deviceIp = this.deviceIpInput.value.trim();
        if (deviceIp === '') {
            // Field is empty, keep button enabled so user can test after entering IP
            this.testConnectionBtn.disabled = false;
        } else {
            // Field has content, disable only if invalid
            this.testConnectionBtn.disabled = !this.validateDeviceIp();
        }

        return isValid;
    }
    
    validateStaffId() {
        const staffId = this.staffIdInput.value.trim();
        const isValid = staffId && /^\d+$/.test(staffId) && parseInt(staffId) >= 1 && parseInt(staffId) <= 65535;
        
        this.updateInputValidation(this.staffIdInput, this.staffIdError, isValid, 
            'Staff ID must be a number between 1 and 65535');
        
        return isValid;
    }
    
    validateDeviceIp() {
        const deviceIp = this.deviceIpInput.value.trim();
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        const isValid = deviceIp && ipRegex.test(deviceIp);
        
        this.updateInputValidation(this.deviceIpInput, this.deviceIpError, isValid, 
            'Please enter a valid IP address (e.g., *************)');
        
        return isValid;
    }
    
    validateStaffName() {
        const staffName = this.staffNameInput.value.trim();
        // Staff name is optional, but if provided, should be reasonable length
        return staffName.length <= 20;
    }
    
    updateInputValidation(input, errorElement, isValid, errorMessage) {
        if (isValid) {
            input.classList.remove('error');
            input.classList.add('success');
            errorElement.textContent = '';
            errorElement.classList.remove('show');
        } else if (input.value.trim() !== '') {
            input.classList.remove('success');
            input.classList.add('error');
            errorElement.textContent = errorMessage;
            errorElement.classList.add('show');
        } else {
            input.classList.remove('error', 'success');
            errorElement.textContent = '';
            errorElement.classList.remove('show');
        }
    }
    
    async testConnection() {
        console.log('Test connection button clicked');

        const deviceIp = this.deviceIpInput.value.trim();
        console.log('Device IP:', deviceIp);

        if (!deviceIp) {
            this.showStatusMessage('Please enter a device IP address first', 'error');
            return;
        }

        if (!this.validateDeviceIp()) {
            this.showStatusMessage('Please enter a valid IP address (e.g., *************)', 'error');
            return;
        }

        this.setButtonLoading(this.testConnectionBtn, this.testLoading, true);
        this.showStatusMessage('🔍 Testing device connection...', 'info');

        try {
            const response = await fetch('/api/device/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    device_ip: this.deviceIpInput.value.trim()
                })
            });

            const result = await response.json();

            if (result.success) {
                let message = '✅ Device connection successful!';
                if (result.connection_type) {
                    message += ` (${result.connection_type})`;
                }
                if (result.note) {
                    message += `\n📝 Note: ${result.note}`;
                }
                this.showStatusMessage(message, 'success');

                // Enable enroll button if connection is successful
                if (this.validateForm()) {
                    this.enrollBtn.disabled = false;
                }
            } else {
                let errorMessage = `❌ Connection failed: ${result.error}`;
                if (result.suggestion) {
                    errorMessage += `\n💡 Suggestion: ${result.suggestion}`;
                }
                this.showStatusMessage(errorMessage, 'error');

                // Show additional troubleshooting info
                this.showTroubleshootingInfo(result.error);
            }
        } catch (error) {
            this.showStatusMessage(`❌ Connection error: ${error.message}`, 'error');
            this.showTroubleshootingInfo('Network error');
        } finally {
            this.setButtonLoading(this.testConnectionBtn, this.testLoading, false);
        }
    }

    showTroubleshootingInfo(errorType) {
        const troubleshootingTips = {
            'timeout': [
                'Check if device is powered on',
                'Verify network connectivity',
                'Try increasing timeout in settings'
            ],
            'refused': [
                'Verify device IP address',
                'Check if device service is running',
                'Ensure port 4370 is not blocked by firewall'
            ],
            'unreachable': [
                'Check network connectivity',
                'Verify device and computer are on same network',
                'Check routing and firewall settings'
            ],
            'default': [
                'Verify device IP address is correct',
                'Check device power and network connection',
                'Ensure device supports TCP/IP communication',
                'Try using the test_connection.py script for detailed diagnostics'
            ]
        };

        const errorLower = errorType.toLowerCase();
        let tips = troubleshootingTips.default;

        if (errorLower.includes('timeout')) {
            tips = troubleshootingTips.timeout;
        } else if (errorLower.includes('refused')) {
            tips = troubleshootingTips.refused;
        } else if (errorLower.includes('unreachable')) {
            tips = troubleshootingTips.unreachable;
        }

        const troubleshootingMessage = '🔧 Troubleshooting tips:\n' +
            tips.map((tip, index) => `${index + 1}. ${tip}`).join('\n');

        setTimeout(() => {
            this.showStatusMessage(troubleshootingMessage, 'info');
        }, 1000);
    }
    
    async handleEnrollment(event) {
        event.preventDefault();
        
        if (!this.validateForm()) {
            this.showStatusMessage('Please fix the form errors before proceeding', 'error');
            return;
        }
        
        if (this.enrollmentInProgress) {
            this.showStatusMessage('Enrollment already in progress', 'info');
            return;
        }
        
        this.setButtonLoading(this.enrollBtn, this.enrollLoading, true);
        this.enrollmentInProgress = true;
        
        try {
            const formData = {
                staff_id: parseInt(this.staffIdInput.value.trim()),
                staff_name: this.staffNameInput.value.trim(),
                device_ip: this.deviceIpInput.value.trim()
            };
            
            const response = await fetch('/api/enroll', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.enrollmentKey = result.enrollment_key;
                this.showEnrollmentDetails(formData);
                this.showStatusMessage('✅ User enrolled successfully! You can now register fingerprint.', 'success');
            } else {
                this.showStatusMessage(`❌ Enrollment failed: ${result.error}`, 'error');
                this.enrollmentInProgress = false;
            }
        } catch (error) {
            this.showStatusMessage(`❌ Enrollment error: ${error.message}`, 'error');
            this.enrollmentInProgress = false;
        } finally {
            this.setButtonLoading(this.enrollBtn, this.enrollLoading, false);
        }
    }
    
    showEnrollmentDetails(formData) {
        this.displayStaffId.textContent = formData.staff_id;
        this.displayStaffName.textContent = formData.staff_name || 'Not provided';
        this.displayDeviceIp.textContent = formData.device_ip;
        this.enrollmentStatus.textContent = 'User created successfully - Ready for fingerprint registration';
        
        this.detailsSection.classList.remove('hidden');
        this.detailsSection.scrollIntoView({ behavior: 'smooth' });
    }
    
    async registerFingerprint() {
        if (!this.enrollmentKey) {
            this.showStatusMessage('No active enrollment found', 'error');
            return;
        }
        
        this.setButtonLoading(this.registerFingerprintBtn, this.fingerprintLoading, true);
        this.fingerprintInstructions.classList.remove('hidden');
        this.cancelEnrollmentBtn.classList.remove('hidden');
        
        this.showProgress(0, 'Initializing fingerprint registration...');
        
        try {
            // Simulate progress updates
            this.updateProgress(25, 'Connecting to device...');
            await this.delay(1000);
            
            this.updateProgress(50, 'Starting fingerprint capture...');
            await this.delay(1000);
            
            const response = await fetch('/api/fingerprint/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    enrollment_key: this.enrollmentKey
                })
            });
            
            this.updateProgress(75, 'Processing fingerprint data...');
            await this.delay(1000);
            
            const result = await response.json();
            
            if (result.success) {
                this.updateProgress(100, 'Fingerprint registered successfully!');
                this.showStatusMessage('🎉 Fingerprint registration completed successfully!', 'success');
                this.enrollmentStatus.textContent = 'Enrollment completed successfully';
                this.completeEnrollment();
            } else {
                this.showStatusMessage(`❌ Fingerprint registration failed: ${result.error}`, 'error');
                this.hideProgress();
            }
        } catch (error) {
            this.showStatusMessage(`❌ Registration error: ${error.message}`, 'error');
            this.hideProgress();
        } finally {
            this.setButtonLoading(this.registerFingerprintBtn, this.fingerprintLoading, false);
        }
    }
    
    async cancelEnrollment() {
        if (!this.enrollmentKey) {
            this.resetForm();
            return;
        }
        
        try {
            await fetch('/api/enrollment/cancel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    enrollment_key: this.enrollmentKey
                })
            });
        } catch (error) {
            console.error('Cancel enrollment error:', error);
        }
        
        this.showStatusMessage('Enrollment cancelled', 'info');
        this.resetForm();
    }
    
    completeEnrollment() {
        setTimeout(() => {
            this.resetForm();
            this.showStatusMessage('You can now enroll another user', 'info');
        }, 3000);
    }
    
    resetForm() {
        this.enrollmentInProgress = false;
        this.enrollmentKey = null;
        
        // Reset form
        this.enrollmentForm.reset();
        
        // Reset validation states
        [this.staffIdInput, this.deviceIpInput].forEach(input => {
            input.classList.remove('error', 'success');
        });
        
        [this.staffIdError, this.deviceIpError].forEach(error => {
            error.textContent = '';
            error.classList.remove('show');
        });
        
        // Hide sections
        this.detailsSection.classList.add('hidden');
        this.fingerprintInstructions.classList.add('hidden');
        this.cancelEnrollmentBtn.classList.add('hidden');
        this.hideProgress();
        
        // Reset buttons
        this.enrollBtn.disabled = true;
        this.testConnectionBtn.disabled = true;
        
        // Clear status messages after a delay
        setTimeout(() => {
            this.statusMessages.innerHTML = '';
        }, 5000);
    }
    
    showProgress(percentage, text) {
        this.progressIndicator.classList.remove('hidden');
        this.updateProgress(percentage, text);
    }
    
    updateProgress(percentage, text) {
        this.progressFill.style.width = `${percentage}%`;
        this.progressText.textContent = text;
    }
    
    hideProgress() {
        this.progressIndicator.classList.add('hidden');
    }
    
    setButtonLoading(button, loadingElement, isLoading) {
        if (isLoading) {
            button.classList.add('loading');
            button.disabled = true;
        } else {
            button.classList.remove('loading');
            button.disabled = false;
        }
    }
    
    showStatusMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `status-message status-${type}`;
        messageDiv.textContent = message;
        
        this.statusMessages.appendChild(messageDiv);
        
        // Auto-remove after 5 seconds for success/info messages
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 5000);
        }
        
        // Scroll to message
        messageDiv.scrollIntoView({ behavior: 'smooth' });
    }
    
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new FingerprintEnrollmentApp();
});
