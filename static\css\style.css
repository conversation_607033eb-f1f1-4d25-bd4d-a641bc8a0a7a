/* Modern CSS for eSSL Fingerprint Enrollment System */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    max-width: 600px;
    width: 100%;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    color: #333;
    font-size: 2.2rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.header p {
    color: #666;
    font-size: 1.1rem;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 1rem;
}

.form-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error {
    border-color: #e74c3c;
    background: #fdf2f2;
}

.form-group input.success {
    border-color: #27ae60;
    background: #f2fdf2;
}

.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 20px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.details-section {
    display: none;
    margin-top: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.details-section h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.user-info {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e1e5e9;
}

.user-info p {
    margin-bottom: 8px;
    color: #555;
}

.user-info strong {
    color: #333;
}

.fingerprint-section {
    text-align: center;
}

.fingerprint-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    border: none;
    padding: 20px 40px;
    border-radius: 10px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 10px;
    min-width: 200px;
}

.fingerprint-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
}

.fingerprint-btn:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.status-message {
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: 500;
    text-align: center;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.status-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 25px;
        margin: 10px;
    }
    
    .header h1 {
        font-size: 1.8rem;
    }
    
    .fingerprint-btn {
        min-width: 150px;
        padding: 15px 25px;
        font-size: 1rem;
    }
}

/* Additional styles for new elements */
.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    margin-bottom: 10px;
}

.btn-secondary:hover {
    box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
}

.btn-cancel {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    margin-top: 15px;
}

.btn-cancel:hover {
    box-shadow: 0 10px 20px rgba(220, 53, 69, 0.3);
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

.progress-indicator {
    margin-top: 20px;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.btn-text {
    display: inline;
}

.btn .loading {
    display: none;
}

.btn.loading .btn-text {
    display: none;
}

.btn.loading .loading {
    display: inline-block;
}

/* Fingerprint instructions styling */
#fingerprintInstructions {
    margin-top: 15px;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced form validation */
.form-group input:invalid {
    border-color: #dc3545;
}

.form-group input:valid {
    border-color: #28a745;
}

/* Status message enhancements */
.status-message {
    position: relative;
    overflow: hidden;
}

.status-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced user info section */
.user-info {
    position: relative;
    transition: all 0.3s ease;
}

.user-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .fingerprint-section {
        padding: 15px;
    }

    .btn-secondary, .btn-cancel {
        width: 100%;
        margin: 5px 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 20px;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .fingerprint-btn {
        width: 100%;
        margin: 5px 0;
    }

    .user-info p {
        font-size: 0.9rem;
    }
}
