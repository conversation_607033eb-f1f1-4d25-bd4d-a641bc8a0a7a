"""
Error Handling Module for eSSL Fingerprint Enrollment System
Provides comprehensive error handling and validation utilities
"""

import logging
import traceback
from functools import wraps
from typing import Dict, Any, Optional, Tuple
import ipaddress
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('essl_enrollment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom exception for validation errors"""
    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(self.message)

class DeviceConnectionError(Exception):
    """Custom exception for device connection errors"""
    def __init__(self, message: str, device_ip: str = None):
        self.message = message
        self.device_ip = device_ip
        super().__init__(self.message)

class EnrollmentError(Exception):
    """Custom exception for enrollment process errors"""
    def __init__(self, message: str, staff_id: int = None, device_ip: str = None):
        self.message = message
        self.staff_id = staff_id
        self.device_ip = device_ip
        super().__init__(self.message)

def handle_api_errors(f):
    """
    Decorator to handle API errors and return consistent JSON responses
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValidationError as e:
            logger.warning(f"Validation error in {f.__name__}: {e.message}")
            return {
                'success': False,
                'error': e.message,
                'field': e.field,
                'error_type': 'validation'
            }, 400
        except DeviceConnectionError as e:
            logger.error(f"Device connection error in {f.__name__}: {e.message}")
            return {
                'success': False,
                'error': e.message,
                'device_ip': e.device_ip,
                'error_type': 'device_connection'
            }, 500
        except EnrollmentError as e:
            logger.error(f"Enrollment error in {f.__name__}: {e.message}")
            return {
                'success': False,
                'error': e.message,
                'staff_id': e.staff_id,
                'device_ip': e.device_ip,
                'error_type': 'enrollment'
            }, 500
        except Exception as e:
            logger.error(f"Unexpected error in {f.__name__}: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': 'Internal server error',
                'error_type': 'internal'
            }, 500
    
    return decorated_function

class InputValidator:
    """
    Comprehensive input validation class
    """
    
    @staticmethod
    def validate_staff_id(staff_id: Any) -> Tuple[bool, Optional[str], Optional[int]]:
        """
        Validate staff ID input
        
        Args:
            staff_id: Staff ID to validate
            
        Returns:
            Tuple of (is_valid, error_message, parsed_staff_id)
        """
        try:
            if staff_id is None or staff_id == '':
                return False, "Staff ID is required", None
            
            # Convert to string first to handle various input types
            staff_id_str = str(staff_id).strip()
            
            if not staff_id_str:
                return False, "Staff ID cannot be empty", None
            
            # Check if it's a valid integer
            if not re.match(r'^\d+$', staff_id_str):
                return False, "Staff ID must be a positive integer", None
            
            staff_id_int = int(staff_id_str)
            
            # Check range (eSSL device user ID range)
            if staff_id_int < 1 or staff_id_int > 65535:
                return False, "Staff ID must be between 1 and 65535", None
            
            return True, None, staff_id_int
            
        except (ValueError, TypeError) as e:
            return False, f"Invalid staff ID format: {str(e)}", None
    
    @staticmethod
    def validate_device_ip(device_ip: Any) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Validate device IP address
        
        Args:
            device_ip: IP address to validate
            
        Returns:
            Tuple of (is_valid, error_message, cleaned_ip)
        """
        try:
            if device_ip is None or device_ip == '':
                return False, "Device IP address is required", None
            
            device_ip_str = str(device_ip).strip()
            
            if not device_ip_str:
                return False, "Device IP address cannot be empty", None
            
            # Validate IP address format
            try:
                ip_obj = ipaddress.ip_address(device_ip_str)
                
                # Check if it's IPv4 (eSSL devices typically use IPv4)
                if not isinstance(ip_obj, ipaddress.IPv4Address):
                    return False, "Only IPv4 addresses are supported", None
                
                # Check if it's not a loopback or reserved address for production use
                if ip_obj.is_loopback:
                    logger.warning(f"Using loopback address: {device_ip_str}")
                
                if ip_obj.is_private:
                    logger.info(f"Using private IP address: {device_ip_str}")
                
                return True, None, device_ip_str
                
            except ipaddress.AddressValueError:
                return False, "Invalid IP address format", None
            
        except Exception as e:
            return False, f"IP validation error: {str(e)}", None
    
    @staticmethod
    def validate_staff_name(staff_name: Any) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Validate staff name input
        
        Args:
            staff_name: Staff name to validate
            
        Returns:
            Tuple of (is_valid, error_message, cleaned_name)
        """
        try:
            if staff_name is None:
                return True, None, ""
            
            staff_name_str = str(staff_name).strip()
            
            # Staff name is optional, so empty is valid
            if not staff_name_str:
                return True, None, ""
            
            # Check length (eSSL device limitation)
            if len(staff_name_str) > 20:
                return False, "Staff name cannot exceed 20 characters", None
            
            # Check for valid characters (alphanumeric, spaces, basic punctuation)
            if not re.match(r'^[a-zA-Z0-9\s\.\-_]+$', staff_name_str):
                return False, "Staff name contains invalid characters", None
            
            return True, None, staff_name_str
            
        except Exception as e:
            return False, f"Name validation error: {str(e)}", None
    
    @staticmethod
    def validate_enrollment_key(enrollment_key: Any) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Validate enrollment key format
        
        Args:
            enrollment_key: Enrollment key to validate
            
        Returns:
            Tuple of (is_valid, error_message, cleaned_key)
        """
        try:
            if enrollment_key is None or enrollment_key == '':
                return False, "Enrollment key is required", None
            
            enrollment_key_str = str(enrollment_key).strip()
            
            if not enrollment_key_str:
                return False, "Enrollment key cannot be empty", None
            
            # Check format (should be device_ip_staff_id)
            if not re.match(r'^[\d\.]+_\d+$', enrollment_key_str):
                return False, "Invalid enrollment key format", None
            
            return True, None, enrollment_key_str
            
        except Exception as e:
            return False, f"Enrollment key validation error: {str(e)}", None

class DeviceErrorHandler:
    """
    Handle device-specific errors and provide user-friendly messages
    """
    
    ERROR_MESSAGES = {
        'connection_timeout': 'Device connection timed out. Please check if the device is powered on and network is accessible.',
        'connection_refused': 'Connection refused by device. Please verify the IP address and ensure the device is not in use.',
        'network_unreachable': 'Network unreachable. Please check your network connection and device IP address.',
        'device_busy': 'Device is currently busy. Please wait and try again.',
        'invalid_response': 'Received invalid response from device. Please check device firmware version.',
        'enrollment_failed': 'Fingerprint enrollment failed. Please ensure finger is clean and placed properly on scanner.',
        'user_exists': 'User with this Staff ID already exists on the device.',
        'device_memory_full': 'Device memory is full. Please clear some users before enrolling new ones.',
        'fingerprint_quality_poor': 'Fingerprint quality is poor. Please clean your finger and try again.',
        'enrollment_timeout': 'Enrollment timed out. Please try again and follow the instructions carefully.'
    }
    
    @classmethod
    def get_user_friendly_message(cls, error_code: str, default_message: str = None) -> str:
        """
        Get user-friendly error message for error code
        
        Args:
            error_code: Error code from device
            default_message: Default message if error code not found
            
        Returns:
            User-friendly error message
        """
        return cls.ERROR_MESSAGES.get(error_code, default_message or "An unknown device error occurred.")
    
    @classmethod
    def categorize_error(cls, error_message: str) -> str:
        """
        Categorize error based on message content
        
        Args:
            error_message: Error message from device
            
        Returns:
            Error category
        """
        error_lower = error_message.lower()
        
        if any(keyword in error_lower for keyword in ['timeout', 'timed out']):
            return 'connection_timeout'
        elif any(keyword in error_lower for keyword in ['refused', 'connection refused']):
            return 'connection_refused'
        elif any(keyword in error_lower for keyword in ['unreachable', 'network']):
            return 'network_unreachable'
        elif any(keyword in error_lower for keyword in ['busy', 'in use']):
            return 'device_busy'
        elif any(keyword in error_lower for keyword in ['invalid', 'malformed']):
            return 'invalid_response'
        elif any(keyword in error_lower for keyword in ['enrollment', 'enroll']):
            return 'enrollment_failed'
        elif any(keyword in error_lower for keyword in ['exists', 'duplicate']):
            return 'user_exists'
        elif any(keyword in error_lower for keyword in ['memory', 'full', 'capacity']):
            return 'device_memory_full'
        elif any(keyword in error_lower for keyword in ['quality', 'poor', 'bad']):
            return 'fingerprint_quality_poor'
        else:
            return 'unknown'

def log_api_request(endpoint: str, data: Dict[str, Any], user_ip: str = None):
    """
    Log API request for audit purposes
    
    Args:
        endpoint: API endpoint
        data: Request data
        user_ip: User IP address
    """
    logger.info(f"API Request - Endpoint: {endpoint}, Data: {data}, User IP: {user_ip}")

def log_device_operation(operation: str, device_ip: str, staff_id: int = None, success: bool = True, error: str = None):
    """
    Log device operation for audit purposes
    
    Args:
        operation: Operation type
        device_ip: Device IP address
        staff_id: Staff ID (if applicable)
        success: Whether operation was successful
        error: Error message (if any)
    """
    log_message = f"Device Operation - {operation}, Device: {device_ip}"
    if staff_id:
        log_message += f", Staff ID: {staff_id}"
    log_message += f", Success: {success}"
    if error:
        log_message += f", Error: {error}"
    
    if success:
        logger.info(log_message)
    else:
        logger.error(log_message)
