"""
Flask Backend API for eSSL Fingerprint Enrollment System
"""

from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import logging
import re
import ipaddress
from essl_device import device_manager
import threading
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Configuration
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['DEBUG'] = True

# Global variables for tracking enrollments
active_enrollments = {}
enrollment_lock = threading.Lock()

def validate_ip_address(ip: str) -> bool:
    """
    Validate IP address format
    
    Args:
        ip (str): IP address to validate
        
    Returns:
        bool: True if valid IP address
    """
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False

def validate_staff_id(staff_id: str) -> bool:
    """
    Validate staff ID format
    
    Args:
        staff_id (str): Staff ID to validate
        
    Returns:
        bool: True if valid staff ID
    """
    try:
        # Check if it's a positive integer
        staff_id_int = int(staff_id)
        return 1 <= staff_id_int <= 65535  # eSSL device user ID range
    except ValueError:
        return False

@app.route('/')
def index():
    """
    Serve the main HTML page
    """
    return render_template('index.html')

@app.route('/api/validate', methods=['POST'])
def validate_inputs():
    """
    Validate staff ID and device IP address
    
    Returns:
        JSON response with validation results
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        staff_id = data.get('staff_id', '').strip()
        device_ip = data.get('device_ip', '').strip()
        
        errors = []
        
        # Validate staff ID
        if not staff_id:
            errors.append('Staff ID is required')
        elif not validate_staff_id(staff_id):
            errors.append('Staff ID must be a number between 1 and 65535')
        
        # Validate device IP
        if not device_ip:
            errors.append('Device IP address is required')
        elif not validate_ip_address(device_ip):
            errors.append('Invalid IP address format')
        
        if errors:
            return jsonify({
                'success': False,
                'errors': errors
            }), 400
        
        return jsonify({
            'success': True,
            'message': 'Validation successful',
            'staff_id': int(staff_id),
            'device_ip': device_ip
        })
        
    except Exception as e:
        logger.error(f"Validation error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@app.route('/api/enroll', methods=['POST'])
def enroll_user():
    """
    Start user enrollment process
    
    Returns:
        JSON response with enrollment result
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        staff_id = data.get('staff_id')
        device_ip = data.get('device_ip', '').strip()
        staff_name = data.get('staff_name', '').strip()
        
        # Validate inputs
        if not validate_staff_id(str(staff_id)):
            return jsonify({
                'success': False,
                'error': 'Invalid staff ID'
            }), 400
        
        if not validate_ip_address(device_ip):
            return jsonify({
                'success': False,
                'error': 'Invalid IP address'
            }), 400
        
        # Convert staff_id to integer
        staff_id = int(staff_id)
        
        # Check if enrollment is already in progress for this staff ID
        with enrollment_lock:
            enrollment_key = f"{device_ip}_{staff_id}"
            if enrollment_key in active_enrollments:
                return jsonify({
                    'success': False,
                    'error': 'Enrollment already in progress for this staff ID'
                }), 409
        
        # Start enrollment process
        logger.info(f"Starting enrollment for Staff ID: {staff_id}, Device IP: {device_ip}")
        
        result = device_manager.enroll_user_fingerprint(
            device_ip=device_ip,
            staff_id=staff_id,
            staff_name=staff_name
        )
        
        if result['success']:
            # Track active enrollment
            with enrollment_lock:
                active_enrollments[enrollment_key] = {
                    'staff_id': staff_id,
                    'device_ip': device_ip,
                    'staff_name': staff_name,
                    'start_time': time.time(),
                    'status': 'waiting_for_fingerprint'
                }
            
            return jsonify({
                'success': True,
                'message': result['message'],
                'staff_id': staff_id,
                'device_ip': device_ip,
                'enrollment_key': result['enrollment_key']
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 500
        
    except Exception as e:
        logger.error(f"Enrollment error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@app.route('/api/fingerprint/register', methods=['POST'])
def register_fingerprint():
    """
    Register fingerprint for enrolled user
    
    Returns:
        JSON response with registration result
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        enrollment_key = data.get('enrollment_key', '').strip()
        
        if not enrollment_key:
            return jsonify({
                'success': False,
                'error': 'Enrollment key is required'
            }), 400
        
        # Check if enrollment exists
        with enrollment_lock:
            if enrollment_key not in active_enrollments:
                return jsonify({
                    'success': False,
                    'error': 'Enrollment not found or expired'
                }), 404
        
        # Check enrollment status
        status_result = device_manager.check_enrollment_status(enrollment_key)
        
        if not status_result['success']:
            # Remove from active enrollments if failed
            with enrollment_lock:
                if enrollment_key in active_enrollments:
                    del active_enrollments[enrollment_key]
            
            return jsonify({
                'success': False,
                'error': status_result['error']
            }), 500
        
        # For this demo, we'll simulate successful fingerprint registration
        # In a real implementation, you would monitor the device for actual fingerprint capture
        
        # Simulate processing time
        time.sleep(2)
        
        # Complete enrollment
        completion_result = device_manager.complete_enrollment(enrollment_key)
        
        # Remove from active enrollments
        with enrollment_lock:
            if enrollment_key in active_enrollments:
                enrollment_info = active_enrollments[enrollment_key]
                del active_enrollments[enrollment_key]
            else:
                enrollment_info = {}
        
        if completion_result['success']:
            return jsonify({
                'success': True,
                'message': 'Fingerprint registered successfully',
                'staff_id': enrollment_info.get('staff_id'),
                'device_ip': enrollment_info.get('device_ip')
            })
        else:
            return jsonify({
                'success': False,
                'error': completion_result['error']
            }), 500
        
    except Exception as e:
        logger.error(f"Fingerprint registration error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@app.route('/api/enrollment/status/<enrollment_key>', methods=['GET'])
def get_enrollment_status(enrollment_key):
    """
    Get enrollment status
    
    Args:
        enrollment_key (str): Enrollment key
        
    Returns:
        JSON response with enrollment status
    """
    try:
        with enrollment_lock:
            if enrollment_key not in active_enrollments:
                return jsonify({
                    'success': False,
                    'error': 'Enrollment not found'
                }), 404
            
            enrollment_info = active_enrollments[enrollment_key]
        
        # Check device status
        status_result = device_manager.check_enrollment_status(enrollment_key)
        
        return jsonify({
            'success': True,
            'enrollment_info': enrollment_info,
            'device_status': status_result
        })
        
    except Exception as e:
        logger.error(f"Get enrollment status error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@app.route('/api/enrollment/cancel', methods=['POST'])
def cancel_enrollment():
    """
    Cancel ongoing enrollment
    
    Returns:
        JSON response with cancellation result
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        enrollment_key = data.get('enrollment_key', '').strip()
        
        if not enrollment_key:
            return jsonify({
                'success': False,
                'error': 'Enrollment key is required'
            }), 400
        
        # Remove from active enrollments
        with enrollment_lock:
            if enrollment_key in active_enrollments:
                del active_enrollments[enrollment_key]
        
        # Cancel device enrollment
        try:
            device_manager.complete_enrollment(enrollment_key)
        except:
            pass  # Ignore errors during cancellation
        
        return jsonify({
            'success': True,
            'message': 'Enrollment cancelled successfully'
        })
        
    except Exception as e:
        logger.error(f"Cancel enrollment error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@app.route('/api/device/test', methods=['POST'])
def test_device_connection():
    """
    Test connection to eSSL device

    Returns:
        JSON response with connection test result
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400

        device_ip = data.get('device_ip', '').strip()

        if not validate_ip_address(device_ip):
            return jsonify({
                'success': False,
                'error': 'Invalid IP address'
            }), 400

        # Test basic network connectivity first
        import socket

        # Test if port is reachable
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.settimeout(5)  # 5 second timeout

        try:
            result = test_socket.connect_ex((device_ip, 4370))
            test_socket.close()

            if result == 0:
                # Port is reachable, now test eSSL protocol
                from essl_device import eSSLDevice

                device = eSSLDevice(device_ip, timeout=5)
                connection_success = device.connect()

                if connection_success:
                    try:
                        status = device.get_device_status()
                        device.disconnect()

                        return jsonify({
                            'success': True,
                            'message': 'Device connection successful',
                            'device_ip': device_ip,
                            'status': status,
                            'connection_type': 'eSSL Protocol'
                        })
                    except Exception as status_error:
                        # Connection works but status failed
                        device.disconnect()
                        return jsonify({
                            'success': True,
                            'message': 'Device connection successful (limited status)',
                            'device_ip': device_ip,
                            'status': {'connected': True, 'status': 'online'},
                            'connection_type': 'eSSL Protocol',
                            'note': 'Device connected but status query failed'
                        })
                else:
                    return jsonify({
                        'success': False,
                        'error': 'Device port is reachable but eSSL protocol connection failed',
                        'device_ip': device_ip,
                        'suggestion': 'Check if device supports eSSL protocol or try different settings'
                    }), 500
            else:
                # Port is not reachable
                return jsonify({
                    'success': False,
                    'error': f'Cannot reach device on port 4370',
                    'device_ip': device_ip,
                    'suggestion': 'Check device IP address, network connectivity, and device power'
                }), 500

        except socket.timeout:
            return jsonify({
                'success': False,
                'error': 'Connection timeout',
                'device_ip': device_ip,
                'suggestion': 'Device may be unreachable or network is slow'
            }), 500
        except Exception as conn_error:
            return jsonify({
                'success': False,
                'error': f'Network connection error: {str(conn_error)}',
                'device_ip': device_ip,
                'suggestion': 'Check network connectivity and device configuration'
            }), 500
        finally:
            try:
                test_socket.close()
            except:
                pass

    except Exception as e:
        logger.error(f"Device test error: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Connection test failed: {str(e)}',
            'suggestion': 'Check device IP address and network connectivity'
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500

# Cleanup function for expired enrollments
def cleanup_expired_enrollments():
    """
    Clean up expired enrollments (runs in background)
    """
    while True:
        try:
            current_time = time.time()
            expired_keys = []
            
            with enrollment_lock:
                for key, enrollment in active_enrollments.items():
                    if current_time - enrollment['start_time'] > 300:  # 5 minutes timeout
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del active_enrollments[key]
                    try:
                        device_manager.complete_enrollment(key)
                    except:
                        pass
            
            if expired_keys:
                logger.info(f"Cleaned up {len(expired_keys)} expired enrollments")
            
            time.sleep(60)  # Check every minute
            
        except Exception as e:
            logger.error(f"Cleanup error: {str(e)}")
            time.sleep(60)

if __name__ == '__main__':
    # Start cleanup thread
    cleanup_thread = threading.Thread(target=cleanup_expired_enrollments, daemon=True)
    cleanup_thread.start()
    
    # Run Flask app
    app.run(host='0.0.0.0', port=5000, debug=True, threaded=True)
