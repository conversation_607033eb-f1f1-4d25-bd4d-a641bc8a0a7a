#!/usr/bin/env python3
"""
Quick Connection Test for eSSL Device
Simple script to test device connectivity
"""

import socket
import sys

def quick_test(ip_address):
    """
    Quick test for device connectivity
    """
    print(f"Testing connection to {ip_address}...")
    
    # Test 1: Basic TCP connection
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((ip_address, 172))
        sock.close()
        
        if result == 0:
            print("✅ Device is reachable on port 172")
            return True
        else:
            print("❌ Cannot connect to device on port 172")
            print("   - Check device IP address")
            print("   - Verify device is powered on")
            print("   - Check network connectivity")
            return False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python quick_test.py <device_ip>")
        print("Example: python quick_test.py *************")
        sys.exit(1)
    
    ip = sys.argv[1]
    quick_test(ip)
