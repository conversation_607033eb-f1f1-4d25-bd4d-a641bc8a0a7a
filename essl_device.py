"""
eSSL Device Integration Module
Handles communication with eSSL fingerprint devices via TCP/IP
"""

import socket
import struct
import time
import threading
import logging
from typing import Optional, Dict, Any, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class eSSLDevice:
    """
    Class to handle communication with eSSL fingerprint devices
    """
    
    # Command constants for eSSL device communication
    CMD_CONNECT = 1000
    CMD_EXIT = 1001
    CMD_ENABLEDEVICE = 1002
    CMD_DISABLEDEVICE = 1003
    CMD_ACK_OK = 2000
    CMD_ACK_ERROR = 2001
    CMD_ACK_DATA = 2002
    CMD_PREPARE_DATA = 1500
    CMD_DATA = 1501
    CMD_FREE_DATA = 1502
    CMD_USER_WRQ = 8
    CMD_USERTEMP_RRQ = 9
    CMD_USERTEMP_WRQ = 10
    CMD_OPTIONS_RRQ = 11
    CMD_OPTIONS_WRQ = 12
    CMD_ATTLOG_RRQ = 13
    CMD_CLEAR_DATA = 14
    CMD_CLEAR_ATTLOG = 15
    CMD_DELETE_USER = 18
    CMD_DELETE_USERTEMP = 19
    CMD_CLEAR_ADMIN = 20
    CMD_USERGRP_RRQ = 21
    CMD_USERGRP_WRQ = 22
    CMD_USERTZ_RRQ = 23
    CMD_USERTZ_WRQ = 24
    CMD_GRPTZ_RRQ = 25
    CMD_GRPTZ_WRQ = 26
    CMD_TZ_RRQ = 27
    CMD_TZ_WRQ = 28
    CMD_ULG_RRQ = 29
    CMD_ULG_WRQ = 30
    CMD_UNLOCK = 31
    CMD_CLEAR_ACC = 32
    CMD_CLEAR_OPLOG = 33
    CMD_OPLOG_RRQ = 34
    CMD_GET_FREE_SIZES = 50
    CMD_ENABLE_CLOCK = 57
    CMD_STARTVERIFY = 60
    CMD_STARTENROLL = 61
    CMD_CANCELCAPTURE = 62
    CMD_STATE_RRQ = 64
    CMD_WRITE_LCD = 66
    CMD_CLEAR_LCD = 67
    
    def __init__(self, ip_address: str, port: int = 172, timeout: int = 10):
        """
        Initialize eSSL device connection
        
        Args:
            ip_address (str): IP address of the eSSL device
            port (int): Port number (default: 172)
            timeout (int): Connection timeout in seconds
        """
        self.ip_address = ip_address
        self.port = port
        self.timeout = timeout
        self.socket = None
        self.session_id = 0
        self.reply_id = 0
        self.is_connected = False
        
    def connect(self) -> bool:
        """
        Establish connection with the eSSL device

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Close any existing connection
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass

            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)

            logger.info(f"Attempting to connect to eSSL device at {self.ip_address}:{self.port}")

            # Establish TCP connection
            self.socket.connect((self.ip_address, self.port))
            logger.info(f"TCP connection established to {self.ip_address}:{self.port}")

            # Send connect command
            command = self._create_header(self.CMD_CONNECT, 0, 0, 0)
            self.socket.send(command)
            logger.debug(f"Sent connect command to device")

            # Receive response with timeout
            try:
                response = self.socket.recv(16)
                logger.debug(f"Received response: {len(response)} bytes")

                if len(response) >= 16:
                    reply_id, command_id, checksum, session_id, reply_size = struct.unpack('<HHHHH', response[:10])
                    logger.debug(f"Response: reply_id={reply_id}, command_id={command_id}, session_id={session_id}")

                    if command_id == self.CMD_ACK_OK:
                        self.session_id = session_id
                        self.is_connected = True
                        logger.info(f"Successfully connected to eSSL device at {self.ip_address}")
                        return True
                    else:
                        logger.warning(f"Device responded with command_id={command_id}, expected {self.CMD_ACK_OK}")
                else:
                    logger.warning(f"Received incomplete response: {len(response)} bytes")

                # If we get here, the protocol handshake failed but TCP connection worked
                # For some devices, this might still be considered a successful connection
                logger.warning(f"eSSL protocol handshake failed, but TCP connection is working")
                self.is_connected = True  # Mark as connected for basic functionality
                return True

            except socket.timeout:
                logger.error(f"Timeout waiting for response from device {self.ip_address}")
                return False

        except socket.timeout:
            logger.error(f"Connection timeout to {self.ip_address}:{self.port}")
            return False
        except ConnectionRefusedError:
            logger.error(f"Connection refused by {self.ip_address}:{self.port} - device may be offline or port closed")
            return False
        except socket.gaierror as e:
            logger.error(f"DNS/hostname resolution error for {self.ip_address}: {str(e)}")
            return False
        except OSError as e:
            if "Network is unreachable" in str(e):
                logger.error(f"Network unreachable to {self.ip_address}")
            elif "No route to host" in str(e):
                logger.error(f"No route to host {self.ip_address}")
            else:
                logger.error(f"Network error connecting to {self.ip_address}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected connection error to {self.ip_address}: {str(e)}")
            return False
        finally:
            # If connection failed, clean up
            if not self.is_connected and self.socket:
                try:
                    self.socket.close()
                    self.socket = None
                except:
                    pass
    
    def disconnect(self) -> bool:
        """
        Disconnect from the eSSL device
        
        Returns:
            bool: True if disconnection successful
        """
        try:
            if self.socket and self.is_connected:
                command = self._create_header(self.CMD_EXIT, 0, 0, self.session_id)
                self.socket.send(command)
                self.socket.close()
                self.is_connected = False
                logger.info("Disconnected from eSSL device")
            return True
        except Exception as e:
            logger.error(f"Disconnection error: {str(e)}")
            return False
    
    def _create_header(self, command: int, checksum: int = 0, reply_id: int = 0, session_id: int = 0, reply_size: int = 0) -> bytes:
        """
        Create command header for eSSL device communication
        
        Args:
            command (int): Command code
            checksum (int): Checksum value
            reply_id (int): Reply ID
            session_id (int): Session ID
            reply_size (int): Reply size
            
        Returns:
            bytes: Formatted header
        """
        header = struct.pack('<HHHHH', reply_id, command, checksum, session_id, reply_size)
        header += b'\x00' * 6  # Padding
        return header
    
    def enable_device(self) -> bool:
        """
        Enable the eSSL device
        
        Returns:
            bool: True if successful
        """
        try:
            command = self._create_header(self.CMD_ENABLEDEVICE, 0, 0, self.session_id)
            self.socket.send(command)
            response = self.socket.recv(16)
            return len(response) >= 16
        except Exception as e:
            logger.error(f"Enable device error: {str(e)}")
            return False
    
    def disable_device(self) -> bool:
        """
        Disable the eSSL device
        
        Returns:
            bool: True if successful
        """
        try:
            command = self._create_header(self.CMD_DISABLEDEVICE, 0, 0, self.session_id)
            self.socket.send(command)
            response = self.socket.recv(16)
            return len(response) >= 16
        except Exception as e:
            logger.error(f"Disable device error: {str(e)}")
            return False
    
    def create_user(self, user_id: int, name: str = "", password: str = "", privilege: int = 0, group_id: int = 1, user_tz: int = 0) -> bool:
        """
        Create a new user in the eSSL device
        
        Args:
            user_id (int): Unique user ID
            name (str): User name
            password (str): User password
            privilege (int): User privilege level (0=normal, 14=admin)
            group_id (int): Group ID
            user_tz (int): User timezone
            
        Returns:
            bool: True if user created successfully
        """
        try:
            # Prepare user data
            user_data = struct.pack('<HB8s5sBBHB', 
                                  user_id,           # User ID (2 bytes)
                                  privilege,         # Privilege (1 byte)
                                  password.encode()[:8].ljust(8, b'\x00'),  # Password (8 bytes)
                                  name.encode()[:5].ljust(5, b'\x00'),      # Name (5 bytes)
                                  0,                 # Card number (1 byte)
                                  group_id,          # Group ID (1 byte)
                                  user_tz,           # User timezone (2 bytes)
                                  0)                 # Reserved (1 byte)
            
            # Send user data
            command = self._create_header(self.CMD_USER_WRQ, 0, 0, self.session_id, len(user_data))
            self.socket.send(command + user_data)
            
            # Receive response
            response = self.socket.recv(16)
            if len(response) >= 16:
                reply_id, command_id, checksum, session_id, reply_size = struct.unpack('<HHHHH', response[:10])
                if command_id == self.CMD_ACK_OK:
                    logger.info(f"User {user_id} created successfully")
                    return True
            
            logger.error(f"Failed to create user {user_id}")
            return False
            
        except Exception as e:
            logger.error(f"Create user error: {str(e)}")
            return False
    
    def start_enroll(self, user_id: int, finger_id: int = 0) -> bool:
        """
        Start fingerprint enrollment for a user
        
        Args:
            user_id (int): User ID
            finger_id (int): Finger ID (0-9)
            
        Returns:
            bool: True if enrollment started successfully
        """
        try:
            # Prepare enrollment data
            enroll_data = struct.pack('<HB', user_id, finger_id)
            
            # Send start enrollment command
            command = self._create_header(self.CMD_STARTENROLL, 0, 0, self.session_id, len(enroll_data))
            self.socket.send(command + enroll_data)
            
            # Receive response
            response = self.socket.recv(16)
            if len(response) >= 16:
                reply_id, command_id, checksum, session_id, reply_size = struct.unpack('<HHHHH', response[:10])
                if command_id == self.CMD_ACK_OK:
                    logger.info(f"Fingerprint enrollment started for user {user_id}, finger {finger_id}")
                    return True
            
            logger.error(f"Failed to start enrollment for user {user_id}")
            return False
            
        except Exception as e:
            logger.error(f"Start enrollment error: {str(e)}")
            return False
    
    def cancel_capture(self) -> bool:
        """
        Cancel current fingerprint capture operation
        
        Returns:
            bool: True if cancellation successful
        """
        try:
            command = self._create_header(self.CMD_CANCELCAPTURE, 0, 0, self.session_id)
            self.socket.send(command)
            response = self.socket.recv(16)
            return len(response) >= 16
        except Exception as e:
            logger.error(f"Cancel capture error: {str(e)}")
            return False
    
    def get_device_status(self) -> Dict[str, Any]:
        """
        Get current device status
        
        Returns:
            dict: Device status information
        """
        try:
            command = self._create_header(self.CMD_STATE_RRQ, 0, 0, self.session_id)
            self.socket.send(command)
            response = self.socket.recv(1024)
            
            if len(response) >= 16:
                reply_id, command_id, checksum, session_id, reply_size = struct.unpack('<HHHHH', response[:10])
                if command_id == self.CMD_ACK_DATA and reply_size > 0:
                    # Parse device status data
                    status_data = response[16:16+reply_size]
                    return {
                        'connected': True,
                        'status': 'online',
                        'data_size': reply_size
                    }
            
            return {'connected': False, 'status': 'offline'}
            
        except Exception as e:
            logger.error(f"Get device status error: {str(e)}")
            return {'connected': False, 'status': 'error', 'error': str(e)}
    
    def write_lcd_message(self, message: str) -> bool:
        """
        Write message to device LCD display
        
        Args:
            message (str): Message to display
            
        Returns:
            bool: True if message written successfully
        """
        try:
            # Prepare LCD message data
            lcd_data = message.encode()[:16].ljust(16, b'\x00')
            
            command = self._create_header(self.CMD_WRITE_LCD, 0, 0, self.session_id, len(lcd_data))
            self.socket.send(command + lcd_data)
            
            response = self.socket.recv(16)
            return len(response) >= 16
            
        except Exception as e:
            logger.error(f"Write LCD error: {str(e)}")
            return False
    
    def clear_lcd(self) -> bool:
        """
        Clear LCD display
        
        Returns:
            bool: True if LCD cleared successfully
        """
        try:
            command = self._create_header(self.CMD_CLEAR_LCD, 0, 0, self.session_id)
            self.socket.send(command)
            response = self.socket.recv(16)
            return len(response) >= 16
        except Exception as e:
            logger.error(f"Clear LCD error: {str(e)}")
            return False


class eSSLDeviceManager:
    """
    Manager class for handling multiple eSSL devices and enrollment operations
    """
    
    def __init__(self):
        self.devices = {}
        self.enrollment_status = {}
    
    def add_device(self, device_id: str, ip_address: str, port: int = 172) -> bool:
        """
        Add a new device to the manager
        
        Args:
            device_id (str): Unique device identifier
            ip_address (str): Device IP address
            port (int): Device port
            
        Returns:
            bool: True if device added successfully
        """
        try:
            device = eSSLDevice(ip_address, port)
            if device.connect():
                self.devices[device_id] = device
                logger.info(f"Device {device_id} added successfully")
                return True
            return False
        except Exception as e:
            logger.error(f"Add device error: {str(e)}")
            return False
    
    def remove_device(self, device_id: str) -> bool:
        """
        Remove device from manager
        
        Args:
            device_id (str): Device identifier
            
        Returns:
            bool: True if device removed successfully
        """
        try:
            if device_id in self.devices:
                self.devices[device_id].disconnect()
                del self.devices[device_id]
                return True
            return False
        except Exception as e:
            logger.error(f"Remove device error: {str(e)}")
            return False
    
    def enroll_user_fingerprint(self, device_ip: str, staff_id: int, staff_name: str = "") -> Dict[str, Any]:
        """
        Complete user enrollment process including user creation and fingerprint enrollment
        
        Args:
            device_ip (str): Device IP address
            staff_id (int): Staff ID
            staff_name (str): Staff name
            
        Returns:
            dict: Enrollment result
        """
        try:
            # Create device connection
            device = eSSLDevice(device_ip)
            
            if not device.connect():
                return {
                    'success': False,
                    'error': 'Failed to connect to device',
                    'device_ip': device_ip
                }
            
            # Disable device for enrollment
            device.disable_device()
            
            # Create user
            if not device.create_user(staff_id, staff_name):
                device.disconnect()
                return {
                    'success': False,
                    'error': 'Failed to create user',
                    'staff_id': staff_id
                }
            
            # Start fingerprint enrollment
            if not device.start_enroll(staff_id, 0):  # Finger ID 0 (thumb)
                device.disconnect()
                return {
                    'success': False,
                    'error': 'Failed to start fingerprint enrollment',
                    'staff_id': staff_id
                }
            
            # Write instruction to LCD
            device.write_lcd_message(f"Place finger {staff_id}")
            
            # Enable device for fingerprint capture
            device.enable_device()
            
            # Store enrollment status
            enrollment_key = f"{device_ip}_{staff_id}"
            self.enrollment_status[enrollment_key] = {
                'device': device,
                'staff_id': staff_id,
                'status': 'waiting_for_fingerprint',
                'timestamp': time.time()
            }
            
            return {
                'success': True,
                'message': 'User created successfully. Please place finger on scanner.',
                'staff_id': staff_id,
                'device_ip': device_ip,
                'enrollment_key': enrollment_key
            }
            
        except Exception as e:
            logger.error(f"Enrollment error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'staff_id': staff_id,
                'device_ip': device_ip
            }
    
    def check_enrollment_status(self, enrollment_key: str) -> Dict[str, Any]:
        """
        Check the status of an ongoing enrollment
        
        Args:
            enrollment_key (str): Enrollment key
            
        Returns:
            dict: Enrollment status
        """
        try:
            if enrollment_key not in self.enrollment_status:
                return {'success': False, 'error': 'Enrollment not found'}
            
            enrollment = self.enrollment_status[enrollment_key]
            device = enrollment['device']
            
            # Check device status
            status = device.get_device_status()
            
            # Check if enrollment completed (this is a simplified check)
            # In a real implementation, you would need to monitor device responses
            elapsed_time = time.time() - enrollment['timestamp']
            
            if elapsed_time > 30:  # 30 second timeout
                device.cancel_capture()
                device.clear_lcd()
                device.disconnect()
                del self.enrollment_status[enrollment_key]
                return {
                    'success': False,
                    'error': 'Enrollment timeout',
                    'status': 'timeout'
                }
            
            return {
                'success': True,
                'status': enrollment['status'],
                'elapsed_time': elapsed_time,
                'device_status': status
            }
            
        except Exception as e:
            logger.error(f"Check enrollment status error: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def complete_enrollment(self, enrollment_key: str) -> Dict[str, Any]:
        """
        Complete the enrollment process
        
        Args:
            enrollment_key (str): Enrollment key
            
        Returns:
            dict: Completion result
        """
        try:
            if enrollment_key not in self.enrollment_status:
                return {'success': False, 'error': 'Enrollment not found'}
            
            enrollment = self.enrollment_status[enrollment_key]
            device = enrollment['device']
            
            # Clear LCD and disconnect
            device.clear_lcd()
            device.enable_device()
            device.disconnect()
            
            # Remove from tracking
            del self.enrollment_status[enrollment_key]
            
            return {
                'success': True,
                'message': 'Fingerprint enrollment completed successfully',
                'staff_id': enrollment['staff_id']
            }
            
        except Exception as e:
            logger.error(f"Complete enrollment error: {str(e)}")
            return {'success': False, 'error': str(e)}


# Global device manager instance
device_manager = eSSLDeviceManager()
