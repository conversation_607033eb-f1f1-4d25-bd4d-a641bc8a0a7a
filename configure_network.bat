@echo off
echo ========================================
echo eSSL Device Network Configuration
echo ========================================
echo.
echo This script will configure your ethernet adapter
echo to communicate with the eSSL device at *************
echo.
echo Current device IP: *************
echo Required computer IP: 172.16.10.x (same subnet)
echo.
pause

echo Configuring ethernet adapter...
echo.

REM Set static IP on ethernet adapter
netsh interface ip set address "Ethernet" static ************* ************* ***********

echo.
echo Configuration applied!
echo Your computer IP: *************
echo Device IP: *************
echo.
echo Testing connection...
ping -n 2 *************

echo.
echo If ping is successful, you can now test the eSSL connection!
echo Run: python quick_test.py *************
pause
