<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eSSL Fingerprint Enrollment System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔐</text></svg>">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>🔐 eSSL Fingerprint Enrollment</h1>
            <p>Secure fingerprint registration system for staff members</p>
        </div>

        <!-- Main Enrollment Form -->
        <form id="enrollmentForm" class="enrollment-form">
            <div class="form-group">
                <label for="staffId">Staff ID</label>
                <input 
                    type="number" 
                    id="staffId" 
                    name="staffId" 
                    placeholder="Enter staff ID (1-65535)" 
                    min="1" 
                    max="65535" 
                    required
                >
                <div class="error-message" id="staffIdError"></div>
            </div>

            <div class="form-group">
                <label for="staffName">Staff Name (Optional)</label>
                <input 
                    type="text" 
                    id="staffName" 
                    name="staffName" 
                    placeholder="Enter staff name" 
                    maxlength="20"
                >
            </div>

            <div class="form-group">
                <label for="deviceIp">Device IP Address</label>
                <input 
                    type="text" 
                    id="deviceIp" 
                    name="deviceIp" 
                    placeholder="Enter device IP (e.g., *************)" 
                    pattern="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
                    required
                >
                <div class="error-message" id="deviceIpError"></div>
            </div>

            <button type="button" id="testConnectionBtn" class="btn btn-secondary">
                <span class="btn-text">Test Connection</span>
                <span class="loading hidden" id="testLoading"></span>
            </button>

            <button type="submit" id="enrollBtn" class="btn">
                <span class="btn-text">Enroll User</span>
                <span class="loading hidden" id="enrollLoading"></span>
            </button>
        </form>

        <!-- Status Messages -->
        <div id="statusMessages"></div>

        <!-- Details Section (Hidden by default) -->
        <div id="detailsSection" class="details-section hidden">
            <h3>📋 Enrollment Details</h3>
            
            <div class="user-info" id="userInfo">
                <p><strong>Staff ID:</strong> <span id="displayStaffId">-</span></p>
                <p><strong>Staff Name:</strong> <span id="displayStaffName">-</span></p>
                <p><strong>Device IP:</strong> <span id="displayDeviceIp">-</span></p>
                <p><strong>Status:</strong> <span id="enrollmentStatus">User created successfully</span></p>
            </div>

            <div class="fingerprint-section">
                <h4>👆 Fingerprint Registration</h4>
                <p>Click the button below to register your fingerprint on the eSSL device.</p>
                
                <button type="button" id="registerFingerprintBtn" class="fingerprint-btn">
                    <span class="btn-text">Register Fingerprint</span>
                    <span class="loading hidden" id="fingerprintLoading"></span>
                </button>

                <div id="fingerprintInstructions" class="hidden">
                    <div class="status-message status-info">
                        <strong>Instructions:</strong><br>
                        1. Place your finger on the scanner<br>
                        2. Keep finger steady until beep<br>
                        3. Lift finger and place again<br>
                        4. Repeat 3 times for best quality
                    </div>
                </div>

                <button type="button" id="cancelEnrollmentBtn" class="btn btn-cancel hidden">
                    Cancel Enrollment
                </button>
            </div>
        </div>

        <!-- Progress Indicator -->
        <div id="progressIndicator" class="progress-indicator hidden">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="progressText">Processing...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
