#!/usr/bin/env python3
"""
eSSL Device Diagnostic Tool
Helps identify the correct port and connection settings
"""

import socket
import subprocess
import platform
import sys

def test_common_ports(ip_address):
    """Test common eSSL device ports"""
    common_ports = [4370, 80, 8080, 172, 443, 23, 8000, 9999]
    
    print(f"🔍 Testing common ports on {ip_address}...")
    open_ports = []
    
    for port in common_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((ip_address, port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port} is OPEN")
                open_ports.append(port)
            else:
                print(f"❌ Port {port} is CLOSED")
        except Exception as e:
            print(f"❌ Port {port} - Error: {e}")
    
    return open_ports

def ping_device(ip_address):
    """Test if device responds to ping"""
    print(f"🏓 Testing ping to {ip_address}...")
    
    try:
        if platform.system().lower() == "windows":
            cmd = ["ping", "-n", "1", "-w", "3000", ip_address]
        else:
            cmd = ["ping", "-c", "1", "-W", "3", ip_address]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ Device responds to ping")
            return True
        else:
            print(f"❌ Device does not respond to ping")
            return False
    except Exception as e:
        print(f"❌ Ping error: {e}")
        return False

def test_web_interface(ip_address):
    """Test if device has a web interface"""
    web_ports = [80, 8080, 443, 8000]
    
    print(f"🌐 Testing web interface on {ip_address}...")
    
    for port in web_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((ip_address, port))
            sock.close()
            
            if result == 0:
                print(f"✅ Web interface might be available at http://{ip_address}:{port}")
                return port
        except:
            pass
    
    print("❌ No web interface detected")
    return None

def get_device_info():
    """Get device information from user"""
    print("📋 Device Information Gathering")
    print("=" * 40)
    
    # Get IP address
    while True:
        ip = input("Enter your eSSL device IP address: ").strip()
        if not ip:
            print("Please enter an IP address")
            continue
        
        try:
            socket.inet_aton(ip)
            break
        except socket.error:
            print("Invalid IP address format. Please try again.")
    
    # Get device model (optional)
    model = input("Enter device model (if known, or press Enter to skip): ").strip()
    
    return ip, model

def comprehensive_diagnosis(ip_address):
    """Run comprehensive diagnosis"""
    print("=" * 60)
    print(f"🔧 eSSL Device Diagnosis for {ip_address}")
    print("=" * 60)
    
    # Test 1: Ping
    ping_success = ping_device(ip_address)
    
    # Test 2: Port scan
    open_ports = test_common_ports(ip_address)
    
    # Test 3: Web interface
    web_port = test_web_interface(ip_address)
    
    # Analysis
    print("\n" + "=" * 60)
    print("📊 DIAGNOSIS RESULTS")
    print("=" * 60)
    
    if not ping_success:
        print("❌ CRITICAL: Device does not respond to ping")
        print("   Possible issues:")
        print("   - Device is powered off")
        print("   - Wrong IP address")
        print("   - Network connectivity issues")
        print("   - Device firewall blocking ping")
        return
    
    if not open_ports:
        print("❌ CRITICAL: No open ports detected")
        print("   Possible issues:")
        print("   - Device services not running")
        print("   - Firewall blocking all ports")
        print("   - Device in wrong mode")
        return
    
    print(f"✅ Device is reachable with {len(open_ports)} open port(s)")
    
    # Analyze open ports
    if 4370 in open_ports:
        print("🎉 EXCELLENT: Standard eSSL port 4370 is open!")
        print("   This is the correct port for eSSL communication")
        print("   Your application should use port 4370")
    elif 80 in open_ports or 8080 in open_ports:
        print("🌐 Web interface detected")
        print(f"   Try accessing: http://{ip_address}")
        if 8080 in open_ports:
            print(f"   Or try: http://{ip_address}:8080")
        print("   Check device settings for TCP/IP communication port")
    else:
        print("⚠️ Non-standard ports detected:")
        for port in open_ports:
            print(f"   - Port {port}")
        print("   Check device manual for correct communication port")
    
    # Recommendations
    print("\n🔧 RECOMMENDATIONS:")
    if 4370 in open_ports:
        print("1. Use port 4370 in your application")
        print("2. Test connection with: python quick_test.py " + ip_address)
    elif web_port:
        print(f"1. Access web interface at http://{ip_address}:{web_port}")
        print("2. Check device network settings")
        print("3. Look for TCP/IP communication settings")
        print("4. Enable TCP/IP communication if disabled")
    else:
        print("1. Check device manual for correct port")
        print("2. Verify device network configuration")
        print("3. Check if device supports TCP/IP communication")

def main():
    """Main function"""
    print("🔧 eSSL Device Diagnostic Tool")
    print("This tool helps identify connection issues with eSSL devices")
    print()
    
    if len(sys.argv) > 1:
        # Command line mode
        ip_address = sys.argv[1]
        model = ""
    else:
        # Interactive mode
        ip_address, model = get_device_info()
    
    comprehensive_diagnosis(ip_address)
    
    print("\n" + "=" * 60)
    print("💡 NEXT STEPS:")
    print("1. If port 4370 is open, change your app back to use port 4370")
    print("2. If web interface is available, check device settings")
    print("3. Consult device manual for TCP/IP communication setup")
    print("4. Contact device manufacturer if issues persist")

if __name__ == "__main__":
    main()
