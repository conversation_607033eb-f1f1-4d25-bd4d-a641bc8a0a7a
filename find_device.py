#!/usr/bin/env python3
"""
Find eSSL devices on the network
Scans common IP ranges for eSSL devices
"""

import socket
import threading
import ipaddress
import time

def scan_ip(ip, port, results):
    """Scan a single IP address"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((str(ip), port))
        sock.close()
        
        if result == 0:
            results.append(str(ip))
            print(f"✅ Found device at {ip}:{port}")
    except:
        pass

def scan_network(network, port=4370):
    """Scan a network range for devices"""
    print(f"🔍 Scanning {network} for devices on port {port}...")
    
    results = []
    threads = []
    
    try:
        net = ipaddress.IPv4Network(network, strict=False)
        
        for ip in net.hosts():
            thread = threading.Thread(target=scan_ip, args=(ip, port, results))
            thread.daemon = True
            thread.start()
            threads.append(thread)
            
            # Limit concurrent threads
            if len(threads) >= 50:
                for t in threads:
                    t.join()
                threads = []
        
        # Wait for remaining threads
        for thread in threads:
            thread.join()
            
    except Exception as e:
        print(f"Error scanning network: {e}")
    
    return results

def get_local_networks():
    """Get local network ranges to scan"""
    import subprocess
    import platform
    
    networks = []
    
    try:
        if platform.system().lower() == "windows":
            # Get Windows network info
            result = subprocess.run(["ipconfig"], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            for line in lines:
                if "IPv4 Address" in line or "IP Address" in line:
                    ip = line.split(':')[-1].strip()
                    if ip and '.' in ip:
                        # Assume /24 network
                        network_base = '.'.join(ip.split('.')[:-1]) + '.0/24'
                        networks.append(network_base)
        else:
            # Linux/Mac
            result = subprocess.run(["ip", "route"], capture_output=True, text=True)
            # Parse route table for local networks
            pass
    except:
        pass
    
    # Default common networks if detection fails
    if not networks:
        networks = [
            "***********/24",
            "***********/24", 
            "10.0.0.0/24",
            "**********/24"
        ]
    
    return networks

def main():
    """Main function"""
    print("🔍 eSSL Device Network Scanner")
    print("=" * 40)
    
    # Get local networks
    networks = get_local_networks()
    print(f"Will scan these networks: {', '.join(networks)}")
    print()
    
    all_devices = []
    
    # Scan each network
    for network in networks:
        devices = scan_network(network)
        all_devices.extend(devices)
        time.sleep(1)  # Brief pause between networks
    
    print("\n" + "=" * 40)
    print("📊 SCAN RESULTS")
    print("=" * 40)
    
    if all_devices:
        print(f"✅ Found {len(all_devices)} device(s):")
        for device in all_devices:
            print(f"   - {device}:4370")
        
        print("\n💡 Try these IP addresses in your application:")
        for device in all_devices:
            print(f"   python quick_test.py {device}")
    else:
        print("❌ No devices found on port 4370")
        print("\nTroubleshooting:")
        print("1. Check if device is powered on")
        print("2. Verify ethernet cable connection")
        print("3. Check if device IP is in different range")
        print("4. Try manual IP configuration on device")
        print("5. Check device manual for default IP")

if __name__ == "__main__":
    main()
