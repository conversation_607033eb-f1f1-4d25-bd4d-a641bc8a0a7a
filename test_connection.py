#!/usr/bin/env python3
"""
eSSL Device Connection Test Script
Use this script to test device connectivity independently
"""

import socket
import sys
import time
from essl_device import eSSLDevice

def test_basic_connectivity(ip_address, port=4370, timeout=5):
    """
    Test basic TCP connectivity to device
    
    Args:
        ip_address (str): Device IP address
        port (int): Device port (default: 4370)
        timeout (int): Connection timeout in seconds
        
    Returns:
        bool: True if port is reachable
    """
    print(f"Testing basic TCP connectivity to {ip_address}:{port}...")
    
    try:
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.settimeout(timeout)
        
        result = test_socket.connect_ex((ip_address, port))
        test_socket.close()
        
        if result == 0:
            print(f"✅ TCP connection successful to {ip_address}:{port}")
            return True
        else:
            print(f"❌ TCP connection failed to {ip_address}:{port} (error code: {result})")
            return False
            
    except Exception as e:
        print(f"❌ TCP connection error: {str(e)}")
        return False

def test_essl_protocol(ip_address, port=4370, timeout=10):
    """
    Test eSSL protocol connectivity
    
    Args:
        ip_address (str): Device IP address
        port (int): Device port (default: 4370)
        timeout (int): Connection timeout in seconds
        
    Returns:
        bool: True if eSSL protocol connection successful
    """
    print(f"Testing eSSL protocol connectivity to {ip_address}:{port}...")
    
    try:
        device = eSSLDevice(ip_address, port, timeout)
        
        if device.connect():
            print(f"✅ eSSL protocol connection successful")
            
            # Test device status
            try:
                status = device.get_device_status()
                print(f"✅ Device status retrieved: {status}")
            except Exception as e:
                print(f"⚠️ Device status query failed: {str(e)}")
            
            # Disconnect
            device.disconnect()
            print(f"✅ Disconnected successfully")
            return True
        else:
            print(f"❌ eSSL protocol connection failed")
            return False
            
    except Exception as e:
        print(f"❌ eSSL protocol error: {str(e)}")
        return False

def ping_test(ip_address):
    """
    Test if device responds to ping
    
    Args:
        ip_address (str): Device IP address
        
    Returns:
        bool: True if ping successful
    """
    print(f"Testing ping to {ip_address}...")
    
    import subprocess
    import platform
    
    try:
        # Determine ping command based on OS
        if platform.system().lower() == "windows":
            cmd = ["ping", "-n", "1", "-w", "3000", ip_address]
        else:
            cmd = ["ping", "-c", "1", "-W", "3", ip_address]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ Ping successful to {ip_address}")
            return True
        else:
            print(f"❌ Ping failed to {ip_address}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Ping timeout to {ip_address}")
        return False
    except Exception as e:
        print(f"❌ Ping error: {str(e)}")
        return False

def comprehensive_test(ip_address, port=4370):
    """
    Run comprehensive connectivity tests
    
    Args:
        ip_address (str): Device IP address
        port (int): Device port
    """
    print("=" * 60)
    print(f"eSSL Device Connection Test")
    print(f"Device IP: {ip_address}")
    print(f"Device Port: {port}")
    print("=" * 60)
    
    # Test 1: Ping
    ping_success = ping_test(ip_address)
    
    # Test 2: Basic TCP connectivity
    tcp_success = test_basic_connectivity(ip_address, port)
    
    # Test 3: eSSL protocol
    essl_success = test_essl_protocol(ip_address, port)
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Ping Test:           {'✅ PASS' if ping_success else '❌ FAIL'}")
    print(f"TCP Connectivity:    {'✅ PASS' if tcp_success else '❌ FAIL'}")
    print(f"eSSL Protocol:       {'✅ PASS' if essl_success else '❌ FAIL'}")
    
    if essl_success:
        print("\n🎉 All tests passed! Device is ready for enrollment.")
    elif tcp_success:
        print("\n⚠️ TCP connection works but eSSL protocol failed.")
        print("   This might indicate:")
        print("   - Device is not an eSSL device")
        print("   - Device firmware doesn't support this protocol")
        print("   - Device is in a different mode")
    elif ping_success:
        print("\n⚠️ Device responds to ping but port 4370 is not accessible.")
        print("   This might indicate:")
        print("   - Device service is not running")
        print("   - Firewall blocking the port")
        print("   - Wrong port number")
    else:
        print("\n❌ Device is not reachable.")
        print("   This might indicate:")
        print("   - Device is powered off")
        print("   - Wrong IP address")
        print("   - Network connectivity issues")
        print("   - Device is on a different network")
    
    print("\nTroubleshooting tips:")
    print("1. Verify device IP address and power status")
    print("2. Check network connectivity")
    print("3. Ensure device and computer are on same network")
    print("4. Check firewall settings")
    print("5. Verify device supports TCP/IP communication")

def interactive_test():
    """
    Interactive test mode
    """
    print("eSSL Device Connection Test Tool")
    print("=" * 40)
    
    while True:
        try:
            ip_address = input("\nEnter device IP address (or 'quit' to exit): ").strip()
            
            if ip_address.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if not ip_address:
                print("Please enter a valid IP address.")
                continue
            
            # Validate IP address format
            try:
                socket.inet_aton(ip_address)
            except socket.error:
                print("Invalid IP address format. Please try again.")
                continue
            
            # Ask for port (optional)
            port_input = input("Enter device port (default: 4370): ").strip()
            if port_input:
                try:
                    port = int(port_input)
                except ValueError:
                    print("Invalid port number. Using default port 4370.")
                    port = 4370
            else:
                port = 4370
            
            # Run tests
            comprehensive_test(ip_address, port)
            
        except KeyboardInterrupt:
            print("\n\nTest interrupted by user.")
            break
        except Exception as e:
            print(f"Error: {str(e)}")

def main():
    """
    Main function
    """
    if len(sys.argv) > 1:
        # Command line mode
        ip_address = sys.argv[1]
        port = int(sys.argv[2]) if len(sys.argv) > 2 else 4370
        comprehensive_test(ip_address, port)
    else:
        # Interactive mode
        interactive_test()

if __name__ == "__main__":
    main()
