#!/usr/bin/env python3
"""
Setup script for eSSL Fingerprint Enrollment System
Handles installation and initial configuration
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Error: Python 3.7 or higher is required")
        print(f"Current version: {platform.python_version()}")
        return False
    print(f"✅ Python version: {platform.python_version()}")
    return True

def install_dependencies():
    """Install required Python packages"""
    print("\n📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = [
        "static/css",
        "static/js", 
        "templates",
        "logs"
    ]
    
    print("\n📁 Creating directories...")
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ Created: {directory}")

def check_files():
    """Check if all required files exist"""
    required_files = [
        "app.py",
        "essl_device.py",
        "error_handler.py",
        "requirements.txt",
        "static/css/style.css",
        "static/js/app.js",
        "templates/index.html"
    ]
    
    print("\n📄 Checking required files...")
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def create_config_file():
    """Create configuration file"""
    config_content = """# eSSL Fingerprint Enrollment System Configuration

# Server Configuration
SERVER_HOST = '0.0.0.0'
SERVER_PORT = 5000
DEBUG_MODE = True

# Device Configuration
DEFAULT_DEVICE_PORT = 172
DEVICE_TIMEOUT = 10
ENROLLMENT_TIMEOUT = 300  # 5 minutes

# Security Configuration
SECRET_KEY = 'your-secret-key-change-this-in-production'

# Logging Configuration
LOG_LEVEL = 'INFO'
LOG_FILE = 'logs/essl_enrollment.log'

# UI Configuration
MAX_STAFF_NAME_LENGTH = 20
MAX_STAFF_ID = 65535
MIN_STAFF_ID = 1
"""
    
    print("\n⚙️ Creating configuration file...")
    with open("config.py", "w") as f:
        f.write(config_content)
    print("✅ Configuration file created: config.py")

def run_tests():
    """Run basic system tests"""
    print("\n🧪 Running basic tests...")
    
    try:
        # Test imports
        import flask
        import socket
        print("✅ Flask import successful")
        
        # Test file permissions
        test_file = "test_permissions.tmp"
        with open(test_file, "w") as f:
            f.write("test")
        os.remove(test_file)
        print("✅ File permissions OK")
        
        return True
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🔧 eSSL Fingerprint Enrollment System Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Check required files
    if not check_files():
        print("\n❌ Some required files are missing. Please ensure all files are present.")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Failed to install dependencies. Please check your internet connection and try again.")
        sys.exit(1)
    
    # Create configuration file
    create_config_file()
    
    # Run tests
    if not run_tests():
        print("\n⚠️ Some tests failed, but installation may still work.")
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Review and update config.py if needed")
    print("2. Ensure your eSSL device is connected to the network")
    print("3. Run the application: python app.py")
    print("4. Open http://localhost:5000 in your browser")
    print("\nFor troubleshooting, see README.md")

if __name__ == "__main__":
    main()
